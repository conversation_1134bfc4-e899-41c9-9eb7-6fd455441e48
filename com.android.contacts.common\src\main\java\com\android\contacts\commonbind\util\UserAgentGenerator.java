/*
 * Copyright (C) 2015 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License
 */

package com.android.contacts.commonbind.util;

import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;

/**
 * Generates a user agent string for the application.
 */
public class UserAgentGenerator {
    /**
     * Builds a user agent string for the current application.  No default implementation.
     *
     * @param context The context.
     * @return The user agent string.
     */
    public static String getUserAgent(Context context) {
       return null;
    }
}
