<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<view
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ex="http://schemas.android.com/apk/res-auto"
    class="com.android.contacts.common.list.ContactTileStarredView"
    android:focusable="true"
    android:background="?android:attr/selectableItemBackground">

    <LinearLayout
        android:id="@+id/contact_tile_push_state"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingTop="24dp">
        <view
            android:id="@+id/contact_tile_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            ex:direction="widthToHeight"
            ex:ratio="1.0"
            class="com.android.contacts.common.widget.ProportionalLayout" >
            <ImageView
                android:id="@+id/contact_tile_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </view>
        <TextView
            android:id="@+id/contact_tile_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="7dp"
            android:textColor="#202020"
            android:textSize="@dimen/contact_browser_list_item_text_size"
            android:singleLine="true"
            android:fadingEdge="horizontal"
            android:fadingEdgeLength="3dip"
            android:ellipsize="marquee"
            android:textAlignment="center"/>
    </LinearLayout>
</view>
