<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<merge xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView
        android:id="@+id/emptyListViewImage"
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:gravity="center_horizontal" />

    <TextView
        android:id="@+id/emptyListViewMessage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal|top"
        android:textSize="@dimen/empty_list_message_text_size"
        android:textColor="@color/empty_list_text_color"
        android:paddingRight="16dp"
        android:paddingLeft="16dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp" />

    <TextView
        android:id="@+id/emptyListViewAction"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:layout_gravity="center_horizontal"
        android:paddingRight="16dp"
        android:paddingLeft="16dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        style="@style/TextActionStyle" />

    <Space
        android:layout_width="match_parent"
        android:layout_height="40dp" />

</merge>
