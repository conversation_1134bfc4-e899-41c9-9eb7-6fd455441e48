apply plugin: 'com.android.application'

android {
    compileSdkVersion 24
    buildToolsVersion "23.0.3"

    defaultConfig {
        applicationId "com.android.dialer"
        minSdkVersion 21
        targetSdkVersion 24
        versionCode 71
        versionName "7.1"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    compile fileTree(include: ['*.jar'], dir: 'libs')
    testCompile 'junit:junit:4.12'
    compile 'com.android.support:appcompat-v7:24.2.0'
    compile project(':com.android.phone.common')
    compile project(':com.android.contacts.common')
    compile 'com.android.support:cardview-v7:24.2.0'
    compile 'com.android.support:recyclerview-v7:24.2.0'
    compile 'com.android.support:design:24.2.0'
    compile 'com.android.support:support-v13:24.2.0'
    compile 'com.umeng.analytics:analytics:latest.integration'
}
