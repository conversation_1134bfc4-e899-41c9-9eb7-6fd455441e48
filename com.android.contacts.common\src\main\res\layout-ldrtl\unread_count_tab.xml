<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<!-- layoutDirection set to ltr as a workaround to a framework bug (b/22010411) causing view with
     layout_centerInParent inside a RelativeLayout to expand to screen width when <PERSON><PERSON> is active -->
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/view_pager_tab_background"
    android:layoutDirection="ltr">
    <!-- The tab icon -->
    <ImageView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"/>
    <TextView
        android:id="@+id/count"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/tab_unread_count_background_size"
        android:layout_marginTop="@dimen/tab_unread_count_margin_top"
        android:layout_marginStart="@dimen/tab_unread_count_margin_left"
        android:layout_toStartOf="@id/icon"
        android:paddingLeft="@dimen/tab_unread_count_text_padding"
        android:paddingRight="@dimen/tab_unread_count_text_padding"
        android:background="@drawable/unread_count_background"
        android:fontFamily="sans-serif-medium"
        android:importantForAccessibility="no"
        android:minWidth="@dimen/tab_unread_count_background_size"
        android:textAlignment="center"
        android:textColor="@color/tab_accent_color"
        android:textSize="@dimen/tab_unread_count_text_size"
        android:layoutDirection="locale"/>
</RelativeLayout>

