<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- A layout representing a single key in the dialpad -->
<com.android.phone.common.dialpad.DialpadKeyButton
    xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/DialpadKeyButtonStyle" >

    <LinearLayout style="@style/DialpadKeyInternalLayoutStyle"
        android:orientation="horizontal"
        android:baselineAligned="false"
        android:layout_gravity="right|center_vertical" >

        <!-- Note in the referenced styles that we assign hard widths to these components
             because we want them to line up vertically when we arrange them in an MxN grid -->

        <com.android.phone.common.dialpad.DialpadTextView
            android:id="@+id/dialpad_key_number"
            style="@style/DialpadKeyNumberStyle"
            android:layout_gravity="right"
            android:layout_marginBottom="0dp"
            android:layout_marginRight="@dimen/dialpad_key_margin_right" />

        <TextView
            android:id="@+id/dialpad_key_letters"
            style="@style/DialpadKeyLettersStyle"
            android:layout_width="@dimen/dialpad_key_text_width"
            android:layout_gravity="right|center" />
    </LinearLayout>
</com.android.phone.common.dialpad.DialpadKeyButton>
