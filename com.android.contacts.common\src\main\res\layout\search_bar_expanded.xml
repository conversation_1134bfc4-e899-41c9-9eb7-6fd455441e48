<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/search_box_expanded"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:visibility="gone" >

    <ImageButton
        android:id="@+id/search_back_button"
        android:layout_width="@dimen/search_box_icon_size"
        android:layout_height="@dimen/search_box_icon_size"
        android:layout_margin="@dimen/search_box_navigation_icon_margin"
        android:src="@drawable/ic_back_arrow"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:contentDescription="@string/action_menu_back_from_search"
        android:tint="@color/actionbar_background_color" />

    <EditText
        android:id="@+id/search_view"
        android:layout_width="0dp"
        android:layout_height="@dimen/search_box_icon_size"
        android:layout_weight="1"
        android:layout_marginLeft="@dimen/search_box_text_left_margin"
        android:textSize="@dimen/search_text_size"
        android:fontFamily="@string/search_font_family"
        android:textColor="@color/searchbox_text_color"
        android:textColorHint="@color/searchbox_hint_text_color"
        android:textCursorDrawable="@drawable/searchedittext_custom_cursor"
        android:background="@null"
        android:inputType="textFilter"
        android:singleLine="true"
        android:imeOptions="flagNoExtractUi" />

    <ImageView
        android:id="@+id/search_close_button"
        android:layout_height="@dimen/search_box_close_icon_size"
        android:layout_width="@dimen/search_box_close_icon_size"
        android:padding="@dimen/search_box_close_icon_padding"
        android:src="@drawable/ic_close_dk"
        android:clickable="true"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:contentDescription="@string/description_clear_search"
        android:tint="@color/searchbox_icon_tint"/>

</LinearLayout>
