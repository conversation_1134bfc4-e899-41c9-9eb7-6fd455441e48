<!--
  ~ Copyright (C) 2012 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<resources>
    <style name="DialpadSpaceStyle">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_weight">3</item>
    </style>

    <style name="DialpadKeyNumberStyle">
        <item name="android:textColor">?attr/dialpad_text_color_primary</item>
        <item name="android:textSize">@dimen/dialpad_key_numbers_size</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">@dimen/dialpad_key_number_margin_bottom</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="DialpadBottomKeyNumberStyle" parent="DialpadKeyNumberStyle">
        <item name="android:layout_marginBottom">@dimen/dialpad_zero_key_number_margin_bottom</item>
    </style>

    <style name="DialpadKeyStarStyle">
        <item name="android:textColor">?attr/dialpad_text_color_secondary</item>
        <item name="android:textSize">@dimen/dialpad_key_star_size</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:alpha">0.8</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">@dimen/dialpad_symbol_margin_bottom</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="DialpadKeyPoundStyle">
        <item name="android:textColor">?attr/dialpad_text_color_secondary</item>
        <item name="android:textSize">@dimen/dialpad_key_pound_size</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:alpha">0.8</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">@dimen/dialpad_symbol_margin_bottom</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="DialpadKeyLettersStyle">
        <item name="android:textColor">?attr/dialpad_text_color_secondary</item>
        <item name="android:textSize">@dimen/dialpad_key_letters_size</item>
        <item name="android:fontFamily">sans-serif-regular</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center_horizontal</item>
    </style>

    <style name="DialpadKeyButtonStyle">
        <item name="android:soundEffectsEnabled">false</item>
        <item name="android:clickable">true</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_weight">13</item>
        <item name="android:minHeight">@dimen/dialpad_key_height</item>
        <item name="android:background">@drawable/btn_dialpad_key</item>
        <item name="android:focusable">true</item>
    </style>

    <style name="DialpadKeyInternalLayoutStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:gravity">center</item>
        <item name="android:orientation">vertical</item>
    </style>

    <style name="Dialpad_Light">
        <item name="dialpad_text_color">@color/dialpad_digits_text_color</item>
        <item name="dialpad_text_color_primary">@color/dialpad_primary_text_color</item>
        <item name="dialpad_text_color_secondary">@color/dialpad_secondary_text_color</item>
        <item name="dialpad_icon_tint">@color/dialpad_icon_tint</item>
        <item name="dialpad_voicemail_tint">@color/dialpad_voicemail_tint</item>
        <item name="dialpad_background">@color/background_dialpad</item>
    </style>

    <style name="Dialpad_Dark">
        <item name="dialpad_text_color">@android:color/white</item>
        <item name="dialpad_text_color_primary">@android:color/white</item>
        <item name="dialpad_text_color_secondary">#ffd4d6d7</item>
        <item name="dialpad_icon_tint">@android:color/white</item>
        <item name="dialpad_voicemail_tint">?attr/dialpad_text_color_secondary</item>
        <item name="dialpad_background">#00000000</item>
    </style>
</resources>
