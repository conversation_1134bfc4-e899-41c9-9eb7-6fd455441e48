<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2009 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="?android:attr/listPreferredItemHeight"
    android:gravity="center_vertical"
    android:paddingLeft="?android:attr/expandableListPreferredItemPaddingLeft"
    android:paddingRight="?android:attr/scrollbarSize"
    android:paddingStart="?android:attr/expandableListPreferredItemPaddingLeft"
    android:paddingEnd="?android:attr/scrollbarSize"
>

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="6dip"
        android:layout_marginEnd="6dip"
        android:layout_marginTop="6dip"
        android:layout_marginBottom="6dip"
        android:layout_weight="1"
        android:duplicateParentState="true"
    >

        <TextView
            android:id="@android:id/text1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:ellipsize="marquee"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:duplicateParentState="true"
        />

        <TextView
            android:id="@android:id/text2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@android:id/text1"
            android:layout_alignLeft="@android:id/text1"
            android:layout_alignStart="@android:id/text1"
            android:maxLines="2"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:duplicateParentState="true"
        />

    </RelativeLayout>

    <CheckBox
        android:id="@android:id/checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="4dip"
        android:layout_marginEnd="4dip"
        android:focusable="false"
        android:clickable="false"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:duplicateParentState="true"
    />

</LinearLayout>
