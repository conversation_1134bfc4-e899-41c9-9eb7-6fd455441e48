<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:focusable="false"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/blocked_numbers_disabled_for_emergency"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="27dp"
        android:paddingBottom="29dp"
        android:paddingStart="@dimen/blocked_number_container_padding"
        android:paddingEnd="44dp"
        android:background="@color/blocked_number_disabled_emergency_background_color"
        android:focusable="true"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            style="@style/BlockedNumbersDescriptionTextStyle"
            android:textStyle="bold"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/blocked_numbers_disabled_emergency_header_label"/>

        <TextView
            style="@style/BlockedNumbersDescriptionTextStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/blocked_numbers_disabled_emergency_desc"/>

    </LinearLayout>

    <android.support.v7.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        card_view:cardCornerRadius="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:focusable="true"
            android:orientation="vertical">

            <TextView
                android:id="@+id/blocked_number_text_view"
                style="@android:style/TextAppearance.Material.Subhead"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:paddingStart="@dimen/blocked_number_container_padding"
                android:gravity="center_vertical"
                android:text="@string/block_list"
                android:textColor="@color/blocked_number_header_color"/>

            <RelativeLayout
                android:id="@+id/import_settings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone">

                <TextView
                    android:id="@+id/import_description"
                    style="@style/BlockedNumbersDescriptionTextStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="11dp"
                    android:paddingBottom="27dp"
                    android:paddingStart="@dimen/blocked_number_container_padding"
                    android:paddingEnd="@dimen/blocked_number_container_padding"
                    android:text="@string/blocked_call_settings_import_description"
                    android:textColor="@color/secondary_text_color"
                    android:textSize="@dimen/blocked_number_settings_description_text_size"/>

                <Button
                    android:id="@+id/import_button"
                    style="@style/DialerFlatButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/blocked_number_container_padding"
                    android:layout_alignParentEnd="true"
                    android:layout_below="@id/import_description"
                    android:text="@string/blocked_call_settings_import_button"/>

                <Button
                    android:id="@+id/view_numbers_button"
                    style="@style/DialerFlatButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_below="@id/import_description"
                    android:layout_toStartOf="@id/import_button"
                    android:text="@string/blocked_call_settings_view_numbers_button"/>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="8dp"
                    android:layout_below="@id/import_button"
                    android:background="@color/divider_line_color"/>

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/migrate_promo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/migrate_promo_header"
                    style="@android:style/TextAppearance.Material.Subhead"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:paddingStart="@dimen/blocked_number_container_padding"
                    android:paddingEnd="@dimen/blocked_number_container_padding"
                    android:gravity="center_vertical"
                    android:textStyle="bold"
                    android:text="@string/migrate_blocked_numbers_dialog_title"
                    android:textColor="@color/blocked_number_header_color"/>

                <TextView
                  android:id="@+id/migrate_promo_description"
                  android:layout_width="match_parent"
                  android:layout_height="wrap_content"
                  android:layout_marginStart="@dimen/blocked_number_container_padding"
                  android:layout_marginEnd="@dimen/blocked_number_container_padding"
                  android:layout_marginBottom="@dimen/blocked_number_container_padding"
                  android:text="@string/migrate_blocked_numbers_dialog_message"
                  android:textColor="@color/secondary_text_color"/>

                <Button
                  android:id="@+id/migrate_promo_allow_button"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:text="@string/migrate_blocked_numbers_dialog_allow_button"
                  android:layout_marginStart="@dimen/blocked_number_container_padding"
                  android:layout_marginEnd="@dimen/blocked_number_container_padding"
                  android:layout_gravity="end"
                  style="@style/DialerPrimaryFlatButtonStyle"
                  android:layout_marginBottom="@dimen/blocked_number_container_padding"/>

                <View
                  style="@style/FullWidthDivider"/>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/add_number_linear_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/blocked_number_add_top_margin"
                android:paddingBottom="@dimen/blocked_number_add_bottom_margin"
                android:paddingStart="@dimen/blocked_number_horizontal_margin"
                android:background="?android:attr/selectableItemBackground"
                android:baselineAligned="false"
                android:clickable="true"
                android:contentDescription="@string/addBlockedNumber"
                android:focusable="true"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/add_number_icon"
                    android:layout_width="@dimen/contact_photo_size"
                    android:layout_height="@dimen/contact_photo_size"
                    android:importantForAccessibility="no"/>
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="@dimen/blocked_number_horizontal_margin"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/add_number_textview"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:text="@string/addBlockedNumber"
                        android:textColor="@color/blocked_number_primary_text_color"
                        android:textSize="@dimen/blocked_number_primary_text_size"/>
                </LinearLayout>

            </LinearLayout>

            <View
                android:id="@+id/blocked_number_list_divider"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="72dp"
                android:background="@color/divider_line_color"/>

        </LinearLayout>

    </android.support.v7.widget.CardView>

</LinearLayout>
