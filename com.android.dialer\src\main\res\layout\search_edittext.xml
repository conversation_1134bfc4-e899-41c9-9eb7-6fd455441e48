<?xml version="1.0" encoding="utf-8"?>
<view class="com.android.dialer.widget.SearchEditTextLayout"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/search_view_container"
    android:orientation="horizontal"
    android:layout_marginTop="@dimen/search_top_margin"
    android:layout_marginBottom="@dimen/search_bottom_margin"
    android:layout_marginLeft="@dimen/search_margin_horizontal"
    android:layout_marginRight="@dimen/search_margin_horizontal"
    android:background="@drawable/rounded_corner"
    android:elevation="@dimen/search_box_elevation">

    <LinearLayout
        android:id="@+id/search_box_collapsed"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingStart="@dimen/search_box_left_padding"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/search_magnifying_glass"
            android:layout_height="@dimen/search_box_icon_size"
            android:layout_width="@dimen/search_box_icon_size"
            android:padding="@dimen/search_box_search_icon_padding"
            android:scaleType="center"
            android:src="@drawable/ic_ab_search"
            android:importantForAccessibility="no"
            android:tint="@color/searchbox_icon_tint" />

        <TextView
            android:id="@+id/search_box_start_search"
            android:layout_height="wrap_content"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_marginLeft="@dimen/search_box_collapsed_text_margin_left"
            android:textSize="@dimen/search_collapsed_text_size"
            android:fontFamily="@string/search_font_family"
            android:textColorHint="@color/searchbox_hint_text_color"
            android:gravity="center_vertical"
            android:hint="@string/dialer_hint_find_contact" />

        <ImageView
            android:id="@+id/voice_search_button"
            android:layout_width="@dimen/search_box_icon_size"
            android:layout_height="match_parent"
            android:src="@drawable/ic_mic_grey600"
            android:scaleType="center"
            android:clickable="true"
            android:contentDescription="@string/description_start_voice_search"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:tint="@color/searchbox_icon_tint" />

        <ImageButton
            android:id="@+id/dialtacts_options_menu_button"
            android:layout_width="@dimen/search_box_icon_size"
            android:layout_height="match_parent"
            android:paddingEnd="@dimen/search_box_right_padding"
            android:scaleType="center"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_overflow_menu"
            android:contentDescription="@string/action_menu_overflow_description"
            android:tint="@color/searchbox_icon_tint" />

    </LinearLayout>

    <include layout="@layout/search_bar_expanded" />

</view>
