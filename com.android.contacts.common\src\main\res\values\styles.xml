<!--
  ~ Copyright (C) 2012 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<resources>

    <style name="DirectoryHeader">
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="SectionHeaderStyle" parent="@android:style/TextAppearance.Large">
        <item name="android:textSize">20sp</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">@color/section_header_text_color</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="DirectoryHeaderStyle" parent="@android:style/TextAppearance.Small">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/dialtacts_secondary_text_color</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <!-- TextView style used for headers.

This is similar to ?android:attr/listSeparatorTextView but uses different
background and text color. See also android:style/Widget.Holo.TextView.ListSeparator
(which is private, so we cannot specify it as a parent style).  -->
    <style name="ContactListSeparatorTextViewStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <!-- See comments for @dimen/list_section_divider_min_height -->
        <item name="android:minHeight">@dimen/list_section_divider_min_height</item>
        <item name="android:background">@drawable/list_section_divider_holo_custom</item>
        <item name="android:textAppearance">@style/DirectoryHeaderStyle</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">8dip</item>
        <item name="android:paddingStart">8dip</item>
        <item name="android:paddingTop">4dip</item>
        <item name="android:paddingBottom">4dip</item>
        <item name="android:ellipsize">end</item>
        <item name="android:singleLine">true</item>
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="TextAppearanceMedium" parent="@android:style/TextAppearance.Medium">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">#000000</item>
    </style>

    <style name="TextAppearanceSmall" parent="@android:style/TextAppearance.Small">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">#737373</item>
    </style>

    <style name="ListViewStyle" parent="@android:style/Widget.Material.Light.ListView">
        <item name="android:overScrollMode">always</item>
    </style>

    <style name="ContactListFilterTheme" parent="@android:Theme.Holo.Light">
        <item name="android:listViewStyle">@style/ListViewStyle</item>
    </style>

    <style name="CustomContactListFilterView" parent="ContactListFilterTheme">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="BackgroundOnlyTheme" parent="@android:style/Theme.Material.Light">
        <item name="android:windowBackground">@null</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowNoTitle">true</item>
        <!-- Activities that use this theme are background activities without obvious displays.
            However, some also have dialogs. Therefore, it doesn't make sense to set this true.-->
        <item name="android:windowNoDisplay">false</item>
        <item name="android:windowIsFloating">true</item>
    </style>

    <style name="Theme.CallSubjectDialogTheme" parent="@android:style/Theme.Material.Light.Dialog">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>

        <!-- No backgrounds, titles or window float -->
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowElevation">0dp</item>
    </style>
</resources>
