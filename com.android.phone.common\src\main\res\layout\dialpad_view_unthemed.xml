<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<view class="com.android.phone.common.dialpad.DialpadView"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/dialpad_view"
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    android:layout_gravity="bottom"
    android:orientation="vertical"
    android:layoutDirection="ltr"
    android:background="?attr/dialpad_background"
    android:clickable="true">

    <!-- Text field where call rate is displayed for ILD calls. -->
    <LinearLayout
        android:id="@+id/rate_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <LinearLayout
            android:id="@+id/ild_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/ild_margin_height"
            android:layout_marginBottom="@dimen/ild_margin_height">

            <TextView android:id="@+id/ild_country"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView android:id="@+id/ild_rate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:textStyle="bold" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#e3e3e3" />

    </LinearLayout>

    <!-- Text field and possibly soft menu button above the keypad where
     the digits are displayed. -->
    <LinearLayout
        android:id="@+id/digits_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dialpad_digits_adjustable_height"
        android:orientation="horizontal">

        <ImageButton android:id="@+id/dialpad_back"
            android:background="@drawable/btn_dialpad_key"
            android:src="@drawable/ic_arrow_back_black_24dp"
            android:tint="?attr/dialpad_icon_tint"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/dialpad_overflow_margin"
            android:paddingLeft="@dimen/dialpad_digits_menu_left_padding"
            android:paddingRight="@dimen/dialpad_digits_menu_right_padding"
            android:contentDescription="@string/description_dialpad_back"
            android:gravity="center"
            android:visibility="gone" />

        <ImageButton android:id="@+id/dialpad_overflow"
            android:background="@drawable/btn_dialpad_key"
            android:src="@drawable/ic_overflow_menu"
            android:tint="?attr/dialpad_icon_tint"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/dialpad_overflow_margin"
            android:paddingLeft="@dimen/dialpad_digits_menu_left_padding"
            android:paddingRight="@dimen/dialpad_digits_menu_right_padding"
            android:contentDescription="@string/description_dialpad_overflow"
            android:gravity="center"
            android:visibility="gone" />

        <view class="com.android.phone.common.dialpad.DigitsEditText"
            xmlns:ex="http://schemas.android.com/apk/res-auto"
            android:id="@+id/digits"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:scrollHorizontally="true"
            android:singleLine="true"
            android:layout_weight="1"
            android:gravity="center"
            android:background="@android:color/transparent"
            android:maxLines="1"
            android:textSize="@dimen/dialpad_digits_adjustable_text_size"
            android:freezesText="true"
            android:focusableInTouchMode="true"
            android:cursorVisible="false"
            android:textColor="?attr/dialpad_text_color"
            android:textCursorDrawable="@null"
            android:fontFamily="sans-serif"
            android:textStyle="normal"
            ex:resizing_text_min_size="@dimen/dialpad_digits_text_min_size" />

        <ImageButton
            android:id="@+id/deleteButton"
            android:background="@drawable/btn_dialpad_key"
            android:tint="?attr/dialpad_icon_tint"
            android:paddingLeft="@dimen/dialpad_digits_padding"
            android:paddingRight="@dimen/dialpad_digits_padding"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:state_enabled="false"
            android:contentDescription="@string/description_delete_button"
            android:src="@drawable/ic_dialpad_delete" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#e3e3e3" />

    <Space
        android:layout_width="match_parent"
        android:layout_height="@dimen/dialpad_space_above_keys" />

    <include layout="@layout/dialpad" />

    <Space
        android:layout_width="match_parent"
        android:layout_height="@dimen/dialpad_space_below_keys" />

</view>
