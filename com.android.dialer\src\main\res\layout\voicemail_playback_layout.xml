<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="64dp"
    android:layout_marginEnd="24dp"
    android:orientation="vertical"
    android:background="@color/background_dialer_call_log_list_item">

    <TextView
        android:id="@+id/playback_state_text"
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:gravity="center"
        android:textSize="14sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingTop="@dimen/voicemail_playback_top_padding">

        <TextView
            android:id="@+id/playback_position_text"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:textSize="14sp"
            android:importantForAccessibility="no" />

        <SeekBar
            android:id="@+id/playback_seek"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:progressDrawable="@drawable/seekbar_drawable"
            android:thumb="@drawable/ic_voicemail_seek_handle"
            android:progress="0"
            android:max="0"
            android:contentDescription="@string/description_playback_seek" />

        <TextView
            android:id="@+id/total_duration_text"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:textSize="14sp"
            android:importantForAccessibility="no" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Space
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"/>

        <ImageButton android:id="@+id/playback_speakerphone"
            style="@style/VoicemailPlaybackLayoutButtonStyle"
            android:src="@drawable/ic_volume_down_24dp"
            android:tint="@color/voicemail_icon_tint"
            android:contentDescription="@string/description_playback_speakerphone" />

        <Space
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"/>

        <ImageButton android:id="@+id/playback_start_stop"
            style="@style/VoicemailPlaybackLayoutButtonStyle"
            android:src="@drawable/ic_play_arrow"
            android:contentDescription="@string/voicemail_play_start_pause" />

        <Space
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"/>

        <ImageButton android:id="@+id/delete_voicemail"
            style="@style/VoicemailPlaybackLayoutButtonStyle"
            android:src="@drawable/ic_delete_24dp"
            android:tint="@color/voicemail_icon_tint"
            android:contentDescription="@string/call_log_trash_voicemail" />

        <Space android:id="@+id/space_before_share_voicemail"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:visibility="gone" />

        <ImageButton android:id="@+id/share_voicemail"
            style="@style/VoicemailPlaybackLayoutButtonStyle"
            android:src="@drawable/ic_share_white_24dp"
            android:tint="@color/voicemail_icon_tint"
            android:contentDescription="@string/call_log_share_voicemail"
            android:visibility="gone" />

        <Space android:id="@+id/space_before_archive_voicemail"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:visibility="gone" />

        <ImageButton android:id="@+id/archive_voicemail"
            style="@style/VoicemailPlaybackLayoutButtonStyle"
            android:src="@drawable/ic_archive_white_24dp"
            android:tint="@color/voicemail_icon_tint"
            android:contentDescription="@string/call_log_archive_voicemail"
            android:visibility="gone" />

        <Space
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"/>

    </LinearLayout>

</LinearLayout>
