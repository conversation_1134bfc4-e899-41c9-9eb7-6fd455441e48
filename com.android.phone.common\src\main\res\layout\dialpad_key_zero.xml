<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- A layout representing the zero key in the dialpad, with the plus sign shifted up because it is
     smaller than a regular letter -->
<com.android.phone.common.dialpad.DialpadKeyButton
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/zero"
    style="@style/DialpadKeyButtonStyle" >

    <LinearLayout style="@style/DialpadKeyInternalLayoutStyle">

        <!-- Note in the referenced styles that we assign hard widths to these components
             because we want them to line up vertically when we arrange them in an MxN grid -->

        <com.android.phone.common.dialpad.DialpadTextView
            android:id="@+id/dialpad_key_number"
            style="@style/DialpadBottomKeyNumberStyle" />

        <TextView
            android:id="@+id/dialpad_key_letters"
            style="@style/DialpadKeyLettersStyle" />
    </LinearLayout>
</com.android.phone.common.dialpad.DialpadKeyButton>
