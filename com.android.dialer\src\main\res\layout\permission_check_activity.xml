<?xml version="1.0" encoding="utf-8"?><!--add by geniusgithub  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical" >


    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="60dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:textSize="18dp"
        android:text="@string/required_permissions_promo" />

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:importantForAccessibility="no"
        android:paddingTop="30dp"
        android:scaleType="centerInside"
        android:src="@drawable/permissions" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@android:color/white" />


</LinearLayout>