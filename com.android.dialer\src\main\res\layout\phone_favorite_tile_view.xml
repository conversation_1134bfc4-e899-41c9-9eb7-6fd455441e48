<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<view
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/contact_tile"
    class="com.android.dialer.list.PhoneFavoriteSquareTileView"
    android:paddingEnd="@dimen/contact_tile_divider_width"
    android:paddingBottom="@dimen/contact_tile_divider_width">

    <RelativeLayout
        android:id="@+id/contact_favorite_card"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="true"
        android:nextFocusRight="@+id/contact_tile_secondary_button">

        <com.android.contacts.common.widget.LayoutSuppressingImageView
            android:id="@+id/contact_tile_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="6" />
            <View
                android:id="@+id/shadow_overlay"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="4"
                android:background="@drawable/shadow_contact_photo" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/contact_tile_text_side_padding"
            android:paddingEnd="@dimen/contact_tile_text_side_padding"
            android:paddingBottom="@dimen/contact_tile_text_bottom_padding"
            android:layout_alignParentBottom="true"
            android:orientation="vertical" >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">
                    <TextView
                        android:id="@+id/contact_tile_name"
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:textColor="@color/contact_tile_name_color"
                        android:fontFamily="sans-serif-medium"
                        android:singleLine="true"
                        android:textSize="15sp"
                        android:fadingEdge="horizontal"
                        android:fadingEdgeLength="3dip"
                        android:ellipsize="marquee"
                        android:textAlignment="viewStart" />
                    <ImageView
                        android:id="@+id/contact_star_icon"
                        android:layout_width="@dimen/favorites_star_icon_size"
                        android:layout_height="@dimen/favorites_star_icon_size"
                        android:layout_marginStart="3dp"
                        android:src="@drawable/ic_star"
                        android:visibility="gone" />
                </LinearLayout>
                <TextView
                    android:id="@+id/contact_tile_phone_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:textColor="@color/contact_tile_name_color"
                    android:fontFamily="sans-serif"
                    android:singleLine="true"
                    android:textSize="11sp"
                    android:fadingEdge="horizontal"
                    android:fadingEdgeLength="3dip"
                    android:ellipsize="marquee"
                    android:textAlignment="viewStart" />
        </LinearLayout>

        <View
            android:id="@+id/contact_tile_push_state"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:importantForAccessibility="no"
            android:background="@drawable/item_background_material_dark" />

        <ImageButton
            android:id="@id/contact_tile_secondary_button"
            android:src="@drawable/ic_more_vert_24dp"
            android:background="@drawable/item_background_material_dark"
            android:layout_height="@dimen/contact_tile_info_button_height_and_width"
            android:layout_width="@dimen/contact_tile_info_button_height_and_width"
            android:paddingLeft="4dp"
            android:paddingRight="9dp"
            android:paddingStart="4dp"
            android:paddingEnd="4dp"
            android:paddingTop="8dp"
            android:paddingBottom="4dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_alignParentEnd="true"
            android:scaleType="center"
            android:contentDescription="@string/description_view_contact_detail" />

    </RelativeLayout>
</view>
