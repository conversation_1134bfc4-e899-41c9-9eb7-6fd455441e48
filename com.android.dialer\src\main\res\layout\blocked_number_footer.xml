<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:focusable="false">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/blocked_number_container_padding"
        android:background="@android:color/white"
        android:focusable="true">

      <TextView android:id="@+id/blocked_number_footer_textview"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:textColor="@color/blocked_number_secondary_text_color"
          android:textSize="@dimen/blocked_number_settings_description_text_size"
          android:text="@string/block_number_footer_message_vvm"/>
    </LinearLayout>
</LinearLayout>
