<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2012 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<resources>

    <!-- If true, enable vibration (haptic feedback) for dialer key presses.
         The pattern is set on a per-platform basis using config_virtualKeyVibePattern.
         TODO: If enough users are annoyed by this, we might eventually
         need to make it a user preference rather than a per-platform
          resource. -->
    <bool name="config_enable_dialer_key_vibration">true</bool>

    <!-- If true, show an onscreen "Dial" button in the dialer.
         In practice this is used on all platforms even the ones with hard SEND/END
         keys, but for maximum flexibility it's controlled by a flag here
         (which can be overridden on a per-product basis.) -->
    <bool name="config_show_onscreen_dial_button">true</bool>

    <!-- Regular expression for prohibiting certain phone numbers in dialpad.
         Ignored if empty. -->
    <string name="config_prohibited_phone_number_regexp"></string>

    <!-- File Authority for AOSP Dialer files -->
    <string name="contacts_file_provider_authority">com.android.dialer.files</string>
</resources>
