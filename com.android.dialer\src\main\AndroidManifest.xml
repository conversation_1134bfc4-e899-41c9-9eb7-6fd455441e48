<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2006 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.android.dialer"
    coreApp="true">

    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="23" />

    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
    <uses-permission android:name="android.permission.READ_PROFILE" />
    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS_PRIVILEGED"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS"/>
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.CONTROL_INCALL_EXPERIENCE" />
    <uses-permission android:name="com.android.voicemail.permission.ADD_VOICEMAIL" />
    <uses-permission android:name="com.android.voicemail.permission.WRITE_VOICEMAIL" />
    <uses-permission android:name="com.android.voicemail.permission.READ_VOICEMAIL" />
    <uses-permission android:name="android.permission.ALLOW_ANY_CODEC_FOR_PLAYBACK" />
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- This tells the activity manager to not delay any of our activity
     start requests, even if they happen immediately after the user
     presses home. -->
    <uses-permission android:name="android.permission.STOP_APP_SWITCHES" />

    <application
        android:name="DialerApplication"
        android:label="@string/applicationLabel"
        android:icon="@mipmap/ic_launcher_phone"
        android:hardwareAccelerated="true"
        android:supportsRtl="true"
        android:backupAgent='com.android.dialer.DialerBackupAgent'>
    <!--         android:usesCleartextTraffic="false"> remove by genius-->

        <meta-data android:name="com.google.android.backup.api_key"
            android:value="AEdPqrEAAAAIBXgtCEKQ6W0PXVnW-ZVia2KmlV2AxsTw3GjAeQ" />

        <meta-data
            android:name="UMENG_APPKEY"
            android:value="57a0a07167e58ef73a001dd6" >
        </meta-data>
        <meta-data
            android:name="UMENG_CHANNEL"
            android:value="Umeng" >
        </meta-data>


        <!-- The entrance point for Phone UI.
             stateAlwaysHidden is set to suppress keyboard show up on
             dialpad screen. -->
        <activity android:name=".DialtactsActivity"
            android:label="@string/launcherActivityLabel"
            android:theme="@style/DialtactsActivityTheme"
            android:launchMode="singleTask"
            android:clearTaskOnLaunch="true"
            android:icon="@mipmap/ic_launcher_phone"
            android:windowSoftInputMode="stateAlwaysHidden|adjustNothing"
            android:resizeableActivity="true"
            android:directBootAware="true">
            <intent-filter>
                <action android:name="android.intent.action.DIAL" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:mimeType="vnd.android.cursor.item/phone" />
                <data android:mimeType="vnd.android.cursor.item/person" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.DIAL" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="voicemail" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.DIAL" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.DIAL" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="tel" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:mimeType="vnd.android.cursor.dir/calls" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.CALL_BUTTON" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <!-- This was never intended to be public, but is here for backward
                 compatibility.  Use Intent.ACTION_DIAL instead. -->
            <intent-filter>
                <action android:name="com.android.phone.action.TOUCH_DIALER" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.TAB" />
            </intent-filter>
            <intent-filter android:label="@string/callHistoryIconLabel">
                <action android:name="com.android.phone.action.RECENT_CALLS" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.TAB" />
            </intent-filter>
            <meta-data
                android:name="com.android.keyguard.layout"
                android:resource="@layout/keyguard_preview" />
        </activity>

        <!--add by geniusgithub -->
        <activity android:name=".ForceRequestPermissionsActivity"
                  android:theme="@style/android:Theme.Material.Light"
                  android:configChanges="orientation|keyboardHidden|screenSize">
        </activity>

        <activity android:name="com.android.dialer.settings.DialerSettingsActivity"
              android:label="@string/dialer_settings_label"
              android:parentActivityName="com.android.dialer.DialtactsActivity"
              android:theme="@style/SettingsStyle"
              android:exported="false">
        </activity>

        <activity android:name="com.android.dialer.filterednumber.BlockedNumbersSettingsActivity"
            android:label="@string/manage_blocked_numbers_label"
            android:parentActivityName="com.android.dialer.settings.DialerSettingsActivity"
            android:theme="@style/ManageBlockedNumbersStyle"
            android:exported="false">
        </activity>

        <activity android:name="com.android.dialer.voicemail.VoicemailArchiveActivity"
            android:label="@string/voicemail_archive_activity_title"
            android:theme="@style/DialtactsThemeWithoutActionBarOverlay">
        </activity>

        <activity android:name="com.android.dialer.calllog.CallLogActivity"
            android:label="@string/call_log_activity_title"
            android:theme="@style/DialtactsThemeWithoutActionBarOverlay"
            android:icon="@mipmap/ic_launcher_phone">
        </activity>

        <activity android:name="com.android.dialer.CallDetailActivity"
            android:label="@string/callDetailTitle"
            android:theme="@style/CallDetailActivityTheme"
            android:icon="@mipmap/ic_launcher_phone">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <data android:mimeType="vnd.android.cursor.item/calls"/>
            </intent-filter>
        </activity>

        <activity android:name="com.android.contacts.common.test.FragmentTestActivity">
            <intent-filter>
                <category android:name="android.intent.category.TEST"/>
            </intent-filter>
        </activity>

        <activity android:name="com.android.contacts.common.dialog.CallSubjectDialog"
                  android:theme="@style/Theme.CallSubjectDialogTheme"
                  android:windowSoftInputMode="stateVisible|adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
            </intent-filter>
        </activity>

        <!-- Backwards compatibility: "Phone" from Gingerbread and earlier -->
        <activity-alias android:name="DialtactsActivity"
            android:targetActivity=".DialtactsActivity"
            android:exported="true"
        />

        <!-- Backwards compatibility: "Call log" from Gingerbread and earlier -->
        <activity-alias android:name="RecentCallsListActivity"
            android:targetActivity=".DialtactsActivity"
            android:exported="true"
        />

        <!-- Backwards compatibility: "Call log" from ICS -->
        <activity-alias android:name=".activities.CallLogActivity"
            android:targetActivity=".DialtactsActivity"
            android:exported="true"
        />

        <!-- Used to filter contacts list by account -->
        <activity
            android:name="com.android.contacts.common.list.AccountFilterActivity"
            android:label="@string/activity_title_contacts_filter"
            android:theme="@style/ContactListFilterTheme"/>

        <!-- Used to select display and sync groups -->
        <activity
            android:name="com.android.contacts.common.list.CustomContactListFilterActivity"
            android:label="@string/custom_list_filter"
            android:theme="@style/ContactListFilterTheme"/>

        <activity
            android:name="com.android.contacts.common.activity.RequestImportVCardPermissionsActivity"
            android:label="@string/launcherActivityLabel"
            android:theme="@style/BackgroundOnlyTheme"
            android:exported="false"/>

        <!-- vCard related -->
        <activity android:name="com.android.contacts.common.vcard.ImportVCardActivity"
                  android:configChanges="orientation|screenSize|keyboardHidden"
                  android:theme="@style/BackgroundOnlyTheme">
        </activity>

        <activity android:name="com.android.contacts.common.vcard.NfcImportVCardActivity"
                  android:configChanges="orientation|screenSize|keyboardHidden"
                  android:theme="@style/BackgroundOnlyTheme">
        </activity>

        <activity android:name="com.android.contacts.common.vcard.CancelActivity"
                  android:theme="@style/BackgroundOnlyTheme"/>

        <activity android:name="com.android.contacts.common.vcard.SelectAccountActivity"
                  android:theme="@style/BackgroundOnlyTheme"/>

        <activity android:name="com.android.contacts.common.vcard.ExportVCardActivity"
                  android:theme="@style/BackgroundOnlyTheme"/>

        <activity android:name="com.android.contacts.common.vcard.ShareVCardActivity"
                  android:theme="@style/BackgroundOnlyTheme" />

        <service
            android:name="com.android.contacts.common.vcard.VCardService"
            android:exported="false"/>
        <!-- end vCard related -->

        <receiver android:name=".calllog.CallLogReceiver">
            <intent-filter>
                <action android:name="android.intent.action.NEW_VOICEMAIL" />
                <data
                    android:scheme="content"
                    android:host="com.android.voicemail"
                    android:mimeType="vnd.android.cursor.item/voicemail"
                />
            </intent-filter>
            <intent-filter android:priority="100">
                 <action android:name="android.intent.action.BOOT_COMPLETED"/>
            </intent-filter>
        </receiver>

        <receiver android:name=".interactions.UndemoteOutgoingCallReceiver">
            <intent-filter>
                <action android:name="android.intent.action.NEW_OUTGOING_CALL" />
            </intent-filter>
        </receiver>

        <service
            android:name=".calllog.CallLogNotificationsService"
            android:directBootAware="true"
            android:exported="false"
        />

        <receiver android:name=".calllog.MissedCallNotificationReceiver"
            android:directBootAware="true">
            <intent-filter>
                <action android:name="android.telecom.action.SHOW_MISSED_CALLS_NOTIFICATION" />
            </intent-filter>
        </receiver>

        <!-- Service to update a contact -->
        <service
            android:name=".contact.ContactUpdateService"
            android:exported="false" />

        <!-- Broadcast receiver that passively listens to location updates -->
        <receiver android:name="com.android.contacts.common.location.CountryDetector$LocationChangedReceiver"/>

        <!-- IntentService to update the user's current country -->
        <service android:name="com.android.contacts.common.location.UpdateCountryService"
            android:exported="false"/>

        <!-- Main in-call UI activity.  This is never launched directly
             from outside the phone app; instead, it's either launched by
             the OutgoingCallBroadcaster (for outgoing calls), or as the
             fullScreenIntent of a notification (for incoming calls.) -->
   <!--     <activity android:name="com.android.incallui.InCallActivity"
                  android:theme="@style/Theme.InCallScreen"
                  android:label="@string/phoneAppLabel"
                  android:excludeFromRecents="true"
                  android:launchMode="singleInstance"
                  android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|keyboardHidden"
                  android:exported="false"
                  android:screenOrientation="nosensor"
                  android:directBootAware="true"
                  android:resizeableActivity="true">
        </activity>

        <service android:name="com.android.incallui.InCallServiceImpl"
                 android:permission="android.permission.BIND_INCALL_SERVICE"
                 android:directBootAware="true" >
            <meta-data android:name="android.telecom.IN_CALL_SERVICE_UI" android:value="true" />
            <meta-data android:name="android.telecom.IN_CALL_SERVICE_RINGING"
                android:value="false"/>
            <intent-filter>
                <action android:name="android.telecom.InCallService"/>
            </intent-filter>
        </service>

        &lt;!&ndash; BroadcastReceiver for receiving Intents from Notification mechanism. &ndash;&gt;
        <receiver android:name="com.android.incallui.NotificationBroadcastReceiver"
            android:directBootAware="true"
            android:exported="false" />-->

        <provider
            android:name=".database.FilteredNumberProvider"
            android:authorities="com.android.dialer.database.filterednumberprovider"
            android:exported="false"
            android:multiprocess="false"
            />

        <provider
            android:name="android.support.v4.content.FileProvider"
            android:authorities="@string/contacts_file_provider_authority"
            android:grantUriPermissions="true"
            android:exported="false">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <provider
            android:name=".database.VoicemailArchiveProvider"
            android:authorities="com.android.dialer.database.voicemailarchiveprovider"
            android:exported="false"
            android:multiprocess="false"
            />
    </application>
</manifest>
