apply plugin: 'com.android.library'

android {
    compileSdkVersion 24
    buildToolsVersion "23.0.3"

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 24
        versionCode 1
        versionName "1.0"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    compile fileTree(include: ['*.jar'], dir: 'libs')
    testCompile 'junit:junit:4.12'
    compile 'com.android.support:appcompat-v7:24.2.0'
    compile project(':com.android.common')
    compile project(':com.android.phone.common')
}
