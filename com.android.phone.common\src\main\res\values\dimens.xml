<!--
  ~ Copyright (C) 2012 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<resources>
    <!-- Text dimensions for dialpad keys -->
    <dimen name="dialpad_key_numbers_size">@dimen/dialpad_key_numbers_default_size</dimen>
    <dimen name="dialpad_key_numbers_default_size">36sp</dimen>
    <dimen name="dialpad_key_letters_size">12sp</dimen>
    <dimen name="dialpad_key_pound_size">23sp</dimen>
    <dimen name="dialpad_key_star_size">36sp</dimen>
    <dimen name="dialpad_key_height">64dp</dimen>
    <dimen name="dialpad_key_number_margin_bottom">@dimen/dialpad_key_number_default_margin_bottom</dimen>
    <dimen name="dialpad_key_number_default_margin_bottom">3dp</dimen>
    <!-- Zero key should have less space between self and text because "+" is smaller -->
    <dimen name="dialpad_zero_key_number_margin_bottom">@dimen/dialpad_zero_key_number_default_margin_bottom</dimen>
    <dimen name="dialpad_zero_key_number_default_margin_bottom">1dp</dimen>
    <dimen name="dialpad_symbol_margin_bottom">13dp</dimen>
    <dimen name="dialpad_key_plus_size">18sp</dimen>
    <dimen name="dialpad_horizontal_padding">5dp</dimen>
    <dimen name="dialpad_digits_text_size">34sp</dimen>
    <dimen name="dialpad_digits_text_min_size">24sp</dimen>
    <dimen name="dialpad_digits_height">60dp</dimen>
    <!-- Provide adjustable dimensions so that different apps can use different dialpad sizes. -->
    <dimen name="dialpad_digits_adjustable_text_size">@dimen/dialpad_digits_text_size</dimen>
    <dimen name="dialpad_digits_adjustable_height">@dimen/dialpad_digits_height</dimen>
    <dimen name="dialpad_digits_padding">16dp</dimen>
    <dimen name="dialpad_digits_menu_left_padding">8dp</dimen>
    <dimen name="dialpad_digits_menu_right_padding">10dp</dimen>
    <dimen name="dialpad_center_margin">3dp</dimen>
    <dimen name="dialpad_button_margin">2dp</dimen>
    <dimen name="dialpad_voicemail_icon_padding_top">2dp</dimen>
    <dimen name="dialpad_key_button_translate_y">100dp</dimen>
    <dimen name="dialpad_overflow_margin">8dp</dimen>
    <dimen name="dialpad_space_above_keys">14dp</dimen>
    <dimen name="dialpad_space_below_keys">8dp</dimen>
    <!-- The bottom space of the dialpad to account for the dial button -->
    <dimen name="dialpad_bottom_space_height">80dp</dimen>

    <!-- Height of the floating action button -->
    <dimen name="floating_action_button_height">56dp</dimen>
    <!-- Width of the floating action button -->
    <dimen name="floating_action_button_width">56dp</dimen>
    <!-- Corner radius of the floating action button -->
    <dimen name="floating_action_button_radius">28dp</dimen>
    <!-- Z translation of the floating action button -->
    <dimen name="floating_action_button_translation_z">8dp</dimen>
    <!-- Padding to be applied to the bottom of lists to make space for the floating action
         button -->
    <dimen name="floating_action_button_list_bottom_padding">88dp</dimen>
    <!-- Right margin of the floating action button -->
    <dimen name="floating_action_button_margin_right">16dp</dimen>
    <!-- Bottom margin of the floating action button -->
    <dimen name="floating_action_button_margin_bottom">16dp</dimen>
    <!-- Offset of bottom margin of the floating action button used when dialpad is up -->
    <dimen name="floating_action_button_dialpad_margin_bottom_offset">4dp</dimen>

    <!-- Top/Bottom padding around the ILD rate display box. -->
    <dimen name="ild_margin_height">10dp</dimen>
</resources>
