<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- Layout showing the type of account filter for phone favorite screen
     (or, new phone "all" screen).
     This is very similar to account_filter_header.xml but different in its
     top padding. -->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/account_filter_header_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="8dip"
    android:layout_marginStart="@dimen/contact_browser_list_header_left_margin"
    android:layout_marginEnd="@dimen/contact_browser_list_header_right_margin"
    android:background="?android:attr/selectableItemBackground"
    android:visibility="gone">
    <TextView
        android:id="@+id/account_filter_header"
        style="@style/ContactListSeparatorTextViewStyle"
        android:paddingStart="@dimen/contact_browser_list_item_text_indent" />
    <TextView
        android:id="@+id/contact_list_all_empty"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="?android:attr/textColorSecondary"
        android:paddingStart="8dip"
        android:paddingTop="@dimen/contact_phone_list_empty_description_padding"
        android:paddingBottom="@dimen/contact_phone_list_empty_description_padding"
        android:textSize="@dimen/contact_phone_list_empty_description_size"
        android:text="@string/listFoundAllContactsZero"
        android:visibility="gone"/>
</LinearLayout>
