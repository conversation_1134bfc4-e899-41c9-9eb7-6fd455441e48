<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.phone.common.dialpad.DialpadKeyButton
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/pound"
    style="@style/DialpadKeyButtonStyle">
    <LinearLayout
        android:orientation="horizontal"
        android:layout_gravity="center_vertical|right"
        style="@style/DialpadKeyInternalLayoutStyle" >
        <com.android.phone.common.dialpad.DialpadTextView
            android:id="@id/dialpad_key_number"
            android:layout_width="@dimen/dialpad_key_number_width"
            android:layout_marginRight="@dimen/dialpad_key_margin_right"
            style="@style/DialpadKeyPoundStyle" />
        <View
            android:layout_width="@dimen/dialpad_key_text_width"
            style="@style/DialpadKeyLettersStyle" />
    </LinearLayout>
</com.android.phone.common.dialpad.DialpadKeyButton>
