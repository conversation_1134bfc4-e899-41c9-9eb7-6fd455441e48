## AndroidDialer
google dialer for Android N

## Example screenshot below:
![github](https://raw.githubusercontent.com/geniusgithub/AndroidDialer/master/storage/m1.jpg "github") 
![github](https://github.com/geniusgithub/AndroidDialer/blob/master/storage/m2.jpg "github")  

##APK DOWNLOAD
* [AndroidDialer.apk](https://raw.githubusercontent.com/geniusgithub/AndroidDialer/master/storage/com.android.dialer.apk)

##LIB:
* com.android.support:appcompat
* com.android.support:appcompat:recycleview
* com.android.support:appcompat:cardview
* com.android.support:appcompat:design
* com.android.support:support-v13

##Run requirements
Android OS 5.0 and up

## Links
cnblog:[http://www.cnblogs.com/lance2016/](http://www.cnblogs.com/lance2016/p/6107376.html)
 
## Development
If you think this article useful Nepal , please pay attention to me<br />
Your support is my motivation, I will continue to strive to do better

##License

     Copyright (C) 2016 The Android Open Source Project
     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.

