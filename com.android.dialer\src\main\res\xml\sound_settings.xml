<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (C) 2014 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android">

    <com.android.dialer.settings.DefaultRingtonePreference
        android:key="@string/ringtone_preference_key"
        android:title="@string/ringtone_title"
        android:dialogTitle="@string/ringtone_title"
        android:persistent="false"
        android:ringtoneType="ringtone" />

    <CheckBoxPreference
        android:key="@string/vibrate_on_preference_key"
        android:title="@string/vibrate_on_ring_title"
        android:persistent="false"
        android:defaultValue="false" />

    <CheckBoxPreference
        android:key="@string/play_dtmf_preference_key"
        android:title="@string/dtmf_tone_enable_title"
        android:persistent="false"
        android:defaultValue="true" />

    <ListPreference
        android:key="@string/dtmf_tone_length_preference_key"
        android:title="@string/dtmf_tone_length_title"
        android:entries="@array/dtmf_tone_length_entries"
        android:entryValues="@array/dtmf_tone_length_entry_values" />

</PreferenceScreen>
