<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2010 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<view
    xmlns:android="http://schemas.android.com/apk/res/android"
    class="com.android.contacts.common.list.ContactListFilterView"
    android:descendantFocusability="blocksDescendants"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/contact_filter_item_min_height"
    android:gravity="center_vertical">

    <ImageView
        android:id="@+id/icon"
        android:scaleType="fitCenter"
        android:layout_width="@dimen/contact_filter_icon_size"
        android:layout_height="@dimen/contact_filter_icon_size"/>

    <LinearLayout
        android:layout_width="0dip"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginLeft="8dip"
        android:layout_marginStart="8dip">

        <TextView
            android:id="@+id/accountType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:singleLine="true"
            android:ellipsize="end"/>

        <TextView
            android:id="@+id/accountUserName"
            android:layout_marginTop="-3dip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:textColor="?android:attr/textColorTertiary"
            android:singleLine="true"
            android:ellipsize="end"/>
    </LinearLayout>

    <RadioButton
        android:id="@+id/radioButton"
        android:clickable="false"
        android:layout_marginTop="1dip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|center_vertical" />
</view>

