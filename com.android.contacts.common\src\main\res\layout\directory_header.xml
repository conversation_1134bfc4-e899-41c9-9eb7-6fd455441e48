<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2009 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- Layout used for list section separators. -->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/DirectoryHeader"
    android:id="@+id/directory_header"
    android:background="?attr/contact_browser_background"
    android:paddingLeft="?attr/list_item_padding_left"
    android:paddingRight="?attr/list_item_padding_right"
    android:paddingStart="?attr/list_item_padding_left"
    android:paddingEnd="?attr/list_item_padding_right"
    android:paddingTop="@dimen/directory_header_extra_top_padding"
    android:paddingBottom="@dimen/directory_header_extra_bottom_padding"
    android:minHeight="@dimen/list_section_divider_min_height"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" >
    <TextView
        android:id="@+id/label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@style/DirectoryHeaderStyle"
        android:singleLine="true"
        android:textAlignment="viewStart" />
    <TextView
        android:id="@+id/display_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textAppearance="@style/DirectoryHeaderStyle"
        android:singleLine="true"
        android:textAlignment="viewStart" />
    <TextView
        android:id="@+id/count"
        android:paddingTop="1dip"
        android:layout_width="0dip"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:singleLine="true"
        android:textAppearance="@style/DirectoryHeaderStyle" />
</LinearLayout>
