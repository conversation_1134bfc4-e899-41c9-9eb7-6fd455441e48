<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2012 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!--  Do not translate. -->
    <string name="dialpad_star_number">*</string>
    <!--  Do not translate. -->
    <string name="dialpad_pound_number">#</string>

    <!--  Do not translate. -->
    <string name="dialpad_0_letters">+</string>
    <!--  Do not translate. -->
    <string name="dialpad_1_letters"></string>
    <!--  Do not translate. -->
    <string name="dialpad_2_letters">ABC</string>
    <!--  Do not translate. -->
    <string name="dialpad_3_letters">DEF</string>
    <!--  Do not translate. -->
    <string name="dialpad_4_letters">GHI</string>
    <!--  Do not translate. -->
    <string name="dialpad_5_letters">JKL</string>
    <!--  Do not translate. -->
    <string name="dialpad_6_letters">MNO</string>
    <!--  Do not translate. -->
    <string name="dialpad_7_letters">PQRS</string>
    <!--  Do not translate. -->
    <string name="dialpad_8_letters">TUV</string>
    <!--  Do not translate. -->
    <string name="dialpad_9_letters">WXYZ</string>
    <!--  Do not translate. -->
    <string name="dialpad_star_letters"></string>
    <!--  Do not translate. -->
    <string name="dialpad_pound_letters"></string>

    <!-- String describing the back button in the dialpad. -->
    <string name="description_dialpad_back">Navigate back</string>

    <!-- String describing the overflow menu button in the dialpad. -->
    <string name="description_dialpad_overflow">More options</string>

    <!-- String describing the Delete/Backspace ImageButton.
         Used by AccessibilityService to announce the purpose of the button.
    -->
    <string name="description_delete_button">backspace</string>

     <!--  String describing the button used to add a plus (+) symbol to the dialpad -->
     <string name="description_image_button_plus">plus</string>

     <!-- String describing the Voicemail ImageButton.
          Used by AccessibilityService to announce the purpose of the button.
     -->
     <string name="description_voicemail_button">voicemail</string>


    <!--  The string used to describe a notification if it is the default one in the system. For
          example, if the user selects the default notification, it will appear as something like
          Default sound(Capella) in the notification summary.
          [CHAR LIMIT=40] -->
    <string name="default_notification_description">Default sound (<xliff:g id="default_sound_title">%1$s</xliff:g>)</string>

    <!-- In-call screen: call failure reason (busy) -->
    <string name="callFailed_userBusy">Line busy</string>
    <!-- In-call screen: call failure reason (network congestion) -->
    <string name="callFailed_congestion">Network busy</string>
    <!-- In-call screen: call failure reason (client timed out) -->
    <string name="callFailed_timedOut">No response, timed out</string>
    <!-- In-call screen: call failure reason (server unreachable) -->
    <string name="callFailed_server_unreachable">Server unreachable</string>
    <!-- In-call screen: call failure reason (peer unreachable) -->
    <string name="callFailed_number_unreachable">Number unreachable</string>
    <!-- In-call screen: call failure reason (incorrect username or password) -->
    <string name="callFailed_invalid_credentials">Incorrect username or password</string>
    <!-- In-call screen: call failure reason (calling from out of network is not allowed) -->
    <string name="callFailed_out_of_network">Out\u2011of\u2011network call</string>
    <!-- In-call screen: call failure reason (server error) -->
    <string name="callFailed_server_error">Server error. Try again later.</string>
    <!-- In-call screen: call failure reason (no signal) -->
    <string name="callFailed_noSignal">No signal</string>
    <!-- In-call screen: call failure reason (GSM ACM limit exceeded) -->
    <string name="callFailed_limitExceeded">ACM limit exceeded</string>
    <!-- In-call screen: call failure reason (radio is off) -->
    <string name="callFailed_powerOff">Radio off</string>
    <!-- In-call screen: call failure reason (SIM error) -->
    <string name="callFailed_simError">No SIM or SIM error</string>
    <!-- In-call screen: call failure reason (out of service) -->
    <string name="callFailed_outOfService">Cellular network not available</string>
    <!-- In-call screen: call failure reason (call denied because of current FDN setting) -->
    <string name="callFailed_fdn_only">Outgoing calls are restricted by FDN.</string>
    <!-- In-call screen: call failure reason (call modified to USSD request) -->
    <string name="callFailed_dialToUssd">DIAL request modified to USSD request.</string>
    <!-- In-call screen: call failure reason (call modified to SS request) -->
    <string name="callFailed_dialToSs">DIAL request modified to SS request.</string>
    <!-- In-call screen: call failure reason (call modified to call with modified data) -->
    <string name="callFailed_dialToDial">DIAL request modified to DIAL with different number.</string>
    <!-- In-call screen: call failure reason (call denied because call barring is on) -->
    <string name="callFailed_cb_enabled">Can\'t make outgoing calls while call barring is on.</string>
    <!-- In-call screen: call failure reason (call denied because domain specific access control is on) -->
    <string name="callFailed_dsac_restricted">Calls restricted by access control.</string>
    <!-- In-call screen: call failure reason (Emergency call denied because domain specific access control is on)-->
    <string name="callFailed_dsac_restricted_emergency">Emergency calls restricted by access control.</string>
    <!-- In-call screen: call failure reason (Normal call denied because domain specific access control is on)-->
    <string name="callFailed_dsac_restricted_normal">Normal calls restricted by access control.</string>
    <!-- In-call screen: call failure reason (Dialed number doesn't exist) -->
    <string name="callFailed_unobtainable_number">Invalid number</string>
    <!-- In-call screen: message displayed in an error dialog -->
    <string name="incall_error_missing_voicemail_number">Voicemail number unknown.</string>
    <!-- In-call screen: call failed because you cannot make video calls when TTY is enabled. -->
    <string name="callFailed_video_call_tty_enabled">Cannot make video calls when TTY is enabled.</string>
    <!-- Choice in the ringtone picker.  If chosen, there will be silence instead of a ringtone played. -->
    <string name="ringtone_silent">None</string>
    <!-- If there is ever a ringtone set for some setting, but that ringtone can no longer be resolved, this is shown instead.  For example, if the ringtone was on a SD card and it had been removed, this would be shown for ringtones on that SD card. -->
    <string name="ringtone_unknown">Unknown ringtone</string>

</resources>
