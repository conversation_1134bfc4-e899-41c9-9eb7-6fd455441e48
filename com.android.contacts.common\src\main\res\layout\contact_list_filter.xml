<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright (C) 2011 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:fillViewport="true">

    <ListView
        android:id="@android:id/list"
        android:layout_width="match_parent"
        android:layout_height="0dip"
        android:layout_weight="1"
        android:layout_marginLeft="@dimen/contact_filter_left_margin"
        android:layout_marginRight="@dimen/contact_filter_right_margin"
        android:layout_marginStart="@dimen/contact_filter_left_margin"
        android:layout_marginEnd="@dimen/contact_filter_right_margin" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dip"
        android:layout_marginLeft="16dip"
        android:layout_marginRight="16dip"
        android:layout_marginStart="16dip"
        android:layout_marginEnd="16dip"
        android:background="?android:attr/dividerHorizontal" />
</LinearLayout>
