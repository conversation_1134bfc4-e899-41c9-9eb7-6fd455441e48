# Copyright (C) 2010 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

option java_package com.android.common

#####
# This file contains definitions for event log (android.util.EventLog) tags
# used by Google Mobile Services applications.  These definitions are part of
# the platform even when the Google applications are not.
#
# See system/core/logcat/event.logtags for a description of the file format.
#
# These event log tags must be assigned specific numbers (no "?") in the range
# 200000-300000.  If new tags are added, be aware that older platforms will be
# missing the tag definitions, and may not be able to show them in their logs.

#####
# System Update (OTA)

# System update status bits
# [31- 9] Reserved for future use
# [ 8- 7] package verified (0=not attempted, 1=succeeded, 2=failed)
# [    6] install approved
# [    5] download approved
# [ 4- 0] status
201001 system_update (status|1|5),(download_result|1|5),(bytes|2|2),(url|3)
201002 system_update_user (action|3)

#####
# Android Market

# @param changes Number of changes made to database in reconstruct
202001 vending_reconstruct (changes|1)
202901 transaction_event (data|3)

#####
# Google Services Framework

203001 sync_details (authority|3),(send|1|2),(recv|1|2),(details|3)

203002 google_http_request (elapsed|2|3),(status|1),(appname|3),(reused|1)

#####
# Google Talk Service

# This event is logged when GTalkService encounters important events
204001 gtalkservice (eventType|1)
# This event is logged for GTalk connection state changes. The status field is an int, but
# it really contains 4 separate values, each taking up a byte
#     (eventType << 24) + (connection state << 16) + (connection error << 8) + network state
204002 gtalk_connection (status|1)

# This event is logged when GTalk connection is closed.
# The status field is an int, but contains 2 different values, it's represented as
#
#     (networkType << 8) + connection error
#
# the possible error values are
#
# no_error=0, no_network=1, connection_failed=2, unknown_host=3, auth_failed=4,
# auth_expired=5, heart_beat_timeout=6, server_error=7, server_reject_rate_limiting=8, unknown=10
#
# duration is the connection duration.
204003 gtalk_conn_close (status|1),(duration|1)

# This event is logged for GTalk heartbeat resets
# interval_and_nt contains both the heartbeat interval and the network type, It's represented as
#     (networkType << 16) + interval
# interval is in seconds; network type can be 0 (mobile) or 1 (wifi); ip is the host ip addr.
204004 gtalk_heartbeat_reset (interval_and_nt|1),(ip|3)

# This event is logged when an Rmq v2 packet is sent or received.
204005 c2dm (packet_type|1),(persistent_id|3),(stream_id|1),(last_stream_id|1)

#####
# Google Login Service and Setup Wizard

# This event is for when communicating to the server times out during account setup
205001 setup_server_timeout
205002 setup_required_captcha (action|3)
205003 setup_io_error (status|3)
205004 setup_server_error
205005 setup_retries_exhausted
205006 setup_no_data_network
205007 setup_completed

205008 gls_account_tried (status|1)
205009 gls_account_saved (status|1)
205010 gls_authenticate (status|1),(service|3)
205011 google_mail_switch (direction|1)

# Event for Android SNet
206001 snet (payload|3)
# TODO(williamluh): Remove this once switch to snet completed.
206003 exp_det_snet (payload|3)

# GoogleBackupTransport uses 207000

# Android Framework Metrics
208000 metrics_heartbeat
