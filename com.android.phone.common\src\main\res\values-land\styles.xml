<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2014 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<resources>
    <style name="DialpadKeyNumberStyle">
        <item name="android:textColor">?attr/dialpad_text_color_primary</item>
        <item name="android:textSize">@dimen/dialpad_key_numbers_size</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:layout_width">@dimen/dialpad_key_number_width</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">@dimen/dialpad_key_number_margin_bottom</item>
    </style>

        <style name="DialpadKeyLettersStyle">
        <item name="android:textColor">?attr/dialpad_text_color_secondary</item>
        <item name="android:textSize">@dimen/dialpad_key_letters_size</item>
        <item name="android:fontFamily">sans-serif-regular</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">left</item>
    </style>
</resources>
