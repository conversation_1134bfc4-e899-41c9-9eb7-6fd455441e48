<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:id="@+id/list_card"
        android:visibility="invisible">
    <View
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="@integer/contact_list_space_layout_weight"
            android:background="@color/background_primary"/>
    <View
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@color/contact_all_list_background_color"
            android:layout_weight="@integer/contact_list_card_layout_weight"
            android:elevation="@dimen/contact_list_card_elevation"/>
    <View
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="@integer/contact_list_space_layout_weight"
            android:background="@color/background_primary"/>
</LinearLayout>
