<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2008 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- android:paddingTop is used instead of android:layout_marginTop. It looks
     android:layout_marginTop is ignored when used with <fragment></fragment>, which
     only happens in Tablet UI since we rely on ViewPager in Phone UI.
     Instead, android:layout_marginTop inside <fragment /> is effective. -->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/pinned_header_list_layout"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/contact_browser_background" >

    <!-- Shown only when an Account filter is set.
         - paddingTop should be here to show "shade" effect correctly. -->
    <include layout="@layout/account_filter_header" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dip"
        android:layout_weight="1" >
        <include layout="@layout/contact_list_card"/>
        <view
            class="com.android.contacts.common.list.PinnedHeaderListView"
            android:id="@android:id/list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="?attr/contact_browser_list_padding_left"
            android:layout_marginRight="?attr/contact_browser_list_padding_right"
            android:layout_marginStart="?attr/contact_browser_list_padding_left"
            android:layout_marginEnd="?attr/contact_browser_list_padding_right"
            android:paddingTop="?attr/list_item_padding_top"
            android:clipToPadding="false"
            android:fastScrollEnabled="true"
            android:fadingEdge="none" />
        <ProgressBar
            android:id="@+id/search_progress"
            style="?android:attr/progressBarStyleLarge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />
    </FrameLayout>

</LinearLayout>
