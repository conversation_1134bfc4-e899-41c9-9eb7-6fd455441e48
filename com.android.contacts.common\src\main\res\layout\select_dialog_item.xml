<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!--
  List item in the pop-up window that appears when tapping a contact's photo
  in the contact editor. This is similar to the framework's select_dialog_item_material.xml layout
  except the text appearance is medium and the padding is set to match the material spec.
-->
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@android:id/text1"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="?android:attr/listPreferredItemHeightSmall"
    android:textAppearance="?android:attr/textAppearanceMedium"
    android:textColor="?android:attr/textColorAlertDialogListItem"
    android:gravity="center_vertical"
    android:paddingStart="24dip"
    android:paddingEnd="24dip"
    android:ellipsize="marquee" />