<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2007 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/CustomContactListFilterView"
    android:orientation="vertical"
    android:fillViewport="true">

    <ExpandableListView
        android:id="@android:id/list"
        android:layout_width="match_parent"
        android:layout_height="0dip"
        android:layout_weight="1"
        android:layout_marginLeft="@dimen/contact_filter_left_margin"
        android:layout_marginRight="@dimen/contact_filter_right_margin"
        android:layout_marginStart="@dimen/contact_filter_left_margin"
        android:layout_marginEnd="@dimen/contact_filter_right_margin"
        android:overScrollMode="always" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dip"
        android:layout_marginLeft="16dip"
        android:layout_marginRight="16dip"
        android:layout_marginStart="16dip"
        android:layout_marginEnd="16dip"
        android:background="?android:attr/dividerHorizontal" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        style="?android:attr/buttonBarStyle">

        <Button
            android:id="@+id/btn_discard"
            style="?android:attr/buttonBarButtonStyle"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@android:string/cancel" />

        <Button
            android:id="@+id/btn_done"
            style="?android:attr/buttonBarButtonStyle"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@android:string/ok" />

    </LinearLayout>
</LinearLayout>
