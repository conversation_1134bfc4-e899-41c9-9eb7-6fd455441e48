<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/pinned_header_list_layout"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Shown only when an Account filter is set.
         - paddingTop should be here to show "shade" effect correctly. -->
    <!-- TODO: Remove the filter header. -->
    <include layout="@layout/account_filter_header" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dip"
        android:layout_weight="1" >
        <view
            class="com.android.contacts.common.list.PinnedHeaderListView"
            style="@style/DialtactsTheme"
            android:id="@android:id/list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="?attr/contact_browser_list_padding_left"
            android:layout_marginEnd="?attr/contact_browser_list_padding_right"
            android:paddingTop="18dp"
            android:fastScrollEnabled="true"
            android:fadingEdge="none"
            android:nestedScrollingEnabled="true" />

        <com.android.dialer.widget.EmptyContentView
            android:id="@+id/empty_list_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone"/>

    </FrameLayout>
</LinearLayout>
