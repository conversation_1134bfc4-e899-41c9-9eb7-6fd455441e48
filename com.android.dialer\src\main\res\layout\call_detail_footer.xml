<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/divider_line_thickness"
        android:background="@color/call_log_action_divider" />

    <TextView android:id="@+id/call_detail_action_block"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/action_block_number"
        android:drawableStart="@drawable/ic_call_detail_block"
        style="@style/CallDetailActionItemStyle" />

    <TextView android:id="@+id/call_detail_action_copy"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/action_copy_number_text"
        android:drawableStart="@drawable/ic_call_detail_content_copy"
        style="@style/CallDetailActionItemStyle" />

    <TextView android:id="@+id/call_detail_action_edit_before_call"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/action_edit_number_before_call"
        android:drawableStart="@drawable/ic_call_detail_edit"
        android:visibility="gone"
        style="@style/CallDetailActionItemStyle" />

    <TextView android:id="@+id/call_detail_action_report"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/action_report_number"
        android:drawableStart="@drawable/ic_call_detail_report"
        android:visibility="gone"
        style="@style/CallDetailActionItemStyle" />

</LinearLayout>
