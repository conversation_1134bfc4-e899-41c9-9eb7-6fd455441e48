<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright (C) 2012 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<resources>

    <!--  Determines the number of columns in a ContactTileRow in the favorites tab -->
    <integer name="contact_tile_column_count_in_favorites">2</integer>
    <integer name="contact_tile_column_count_in_favorites_new">3</integer>

    <!-- The number of characters in the snippet before we need to tokenize and ellipse. -->
    <integer name="snippet_length_before_tokenize">30</integer>

    <!-- Layout weight of space elements in contact list view.
    Default to 0 to indicate no padding-->
    <integer name="contact_list_space_layout_weight">0</integer>
    <!-- Layout weight of card in contact list view.
    Default to 0 to indicate no padding -->
    <integer name="contact_list_card_layout_weight">0</integer>

    <!-- Duration of the animations on the call subject dialog. -->
    <integer name="call_subject_animation_duration">250</integer>

    <!-- A big number to make sure "About contacts" always showing at the bottom of Settings.-->
    <integer name="about_contacts_order_number">100</integer>
</resources>
