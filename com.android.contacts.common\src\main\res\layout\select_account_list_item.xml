<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- Layout of a single item in the InCallUI Account Chooser Dialog. -->
<view class="com.android.contacts.common.widget.ActivityTouchLinearLayout"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="8dp" >

    <ImageView android:id="@+id/icon"
               android:layout_width="48dp"
               android:layout_height="48dp"
               android:scaleType="center" />

    <LinearLayout
              android:id="@+id/text"
              android:gravity="start|center_vertical"
              android:layout_marginLeft="8dp"
              android:layout_width="0dp"
              android:layout_weight="1"
              android:layout_height="match_parent"
              android:orientation="vertical" >
         <TextView android:id="@+id/label"
                   android:textAppearance="?android:attr/textAppearanceMedium"
                   android:textColor="@color/dialtacts_primary_text_color"
                   android:includeFontPadding="false"
                   android:layout_width="match_parent"
                   android:layout_height="wrap_content" />
         <TextView android:id="@+id/number"
                   android:textAppearance="?android:attr/textAppearanceSmall"
                   android:includeFontPadding="false"
                   android:layout_width="match_parent"
                   android:layout_height="wrap_content"
                   android:visibility="gone" />
    </LinearLayout>

</view>
