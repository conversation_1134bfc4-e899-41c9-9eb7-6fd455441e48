<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/caller_information"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/call_detail_horizontal_margin"
    android:paddingTop="@dimen/call_detail_top_margin"
    android:paddingBottom="@dimen/call_detail_bottom_margin"
    android:baselineAligned="false"
    android:orientation="horizontal"
    android:elevation="@dimen/call_detail_elevation"
    android:focusable="true"
    android:background="@color/background_dialer_white" >

    <QuickContactBadge
        android:id="@+id/quick_contact_photo"
        android:layout_width="@dimen/contact_photo_size"
        android:layout_height="@dimen/contact_photo_size"
        android:layout_alignParentStart="true"
        android:layout_gravity="top"
        android:layout_marginTop="3dp"
        android:focusable="true" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center_vertical"
        android:layout_marginStart="@dimen/call_detail_horizontal_margin">

        <TextView
            android:id="@+id/caller_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?attr/call_log_primary_text_color"
            android:textSize="@dimen/call_log_primary_text_size"
            android:includeFontPadding="false"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="3dp"
            android:singleLine="true" />

        <TextView
            android:id="@+id/caller_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?attr/call_log_secondary_text_color"
            android:textSize="@dimen/call_log_detail_text_size"
            android:layout_marginBottom="1dp"
            android:singleLine="true" />

        <TextView
            android:id="@+id/phone_account_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?attr/call_log_secondary_text_color"
            android:textSize="@dimen/call_log_detail_text_size"
            android:singleLine="true"
            android:visibility="gone" />

    </LinearLayout>

    <ImageView
        android:id="@+id/call_back_button"
        android:layout_width="@dimen/call_log_list_item_primary_action_dimen"
        android:layout_height="@dimen/call_log_list_item_primary_action_dimen"
        android:layout_marginEnd="16dp"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:src="@drawable/ic_call_24dp"
        android:scaleType="center"
        android:tint="@color/call_log_list_item_primary_action_icon_tint"
        android:contentDescription="@string/description_call_log_call_action"
        android:visibility="gone" />

</LinearLayout>
