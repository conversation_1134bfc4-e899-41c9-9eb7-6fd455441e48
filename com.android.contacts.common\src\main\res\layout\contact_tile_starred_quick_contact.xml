<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<view
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:paddingBottom="1dip"
    android:paddingRight="1dip"
    android:paddingEnd="1dip"
    android:background="@null"
    class="com.android.contacts.common.list.ContactTileStarredView" >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent" >

        <com.android.contacts.common.widget.LayoutSuppressingImageView
            android:id="@+id/contact_tile_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/contact_tile_shadowbox_height"
            android:orientation="vertical"
            android:background="@color/contact_tile_shadow_box_color"
            android:layout_alignParentBottom="true"
            android:gravity="center_vertical"
            android:paddingLeft="8dip"
            android:paddingRight="8dip"
            android:paddingStart="8dip"
            android:paddingEnd="8dip">

            <TextView
                android:id="@+id/contact_tile_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:singleLine="true"
                android:fadingEdge="horizontal"
                android:fadingEdgeLength="3dip"
                android:ellipsize="marquee"
                android:textAlignment="viewStart" />

            <TextView
                android:id="@+id/contact_tile_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:textColor="@color/people_contact_tile_status_color"
                android:singleLine="true"
                android:drawablePadding="4dip"
                android:fadingEdge="horizontal"
                android:fadingEdgeLength="3dip"
                android:layout_marginTop="-3dip"
                android:ellipsize="marquee" />

        </LinearLayout>

        <QuickContactBadge
            android:id="@+id/contact_tile_quick"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:focusable="true"
            android:background="@null" />

    </RelativeLayout>

</view>
