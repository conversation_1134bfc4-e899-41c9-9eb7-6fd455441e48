<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="true">
        <layer-list xmlns:android="http://schemas.android.com/apk/res/android">
          <item android:id="@android:id/background">
            <shape android:shape="line">
              <stroke
                android:width="2dip"
                android:color="@color/voicemail_playback_seek_bar_yet_to_play"
                />
            </shape>
          </item>
          <!-- I am not defining a secondary progress colour - we don't use it. -->
          <item android:id="@android:id/progress">
            <clip>
              <shape android:shape="line">
                <stroke
                  android:width="2dip"
                  android:color="@color/voicemail_playback_seek_bar_already_played"
                  />
              </shape>
            </clip>
          </item>
        </layer-list>
    </item>
    <item>
        <layer-list xmlns:android="http://schemas.android.com/apk/res/android">
          <item android:id="@android:id/background">
            <shape android:shape="line">
              <stroke
                android:width="2dip"
                android:color="@color/voicemail_playback_seek_bar_yet_to_play"
                />
            </shape>
          </item>
          <!-- I am not defining a secondary progress colour - we don't use it. -->
          <item android:id="@android:id/progress">
            <clip>
              <shape android:shape="line">
                <stroke
                  android:width="2dip"
                  android:color="@color/voicemail_playback_seek_bar_yet_to_play"
                  />
              </shape>
            </clip>
          </item>
        </layer-list>
    </item>
</selector>
