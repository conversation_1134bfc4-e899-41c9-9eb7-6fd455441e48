<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2008 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- Layout of a single item in the Dialer's "Dialpad chooser" UI. -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView android:id="@+id/icon"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:scaleType="center" />

    <TextView android:id="@+id/text"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="@color/dialpad_primary_text_color"
        android:layout_gravity="center_vertical"
        android:layout_width="0dip"
        android:layout_weight="1"
        android:layout_height="wrap_content" />

</LinearLayout>
