<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lists_frame"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true" >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- TODO: Apply background color to ActionBar instead of a FrameLayout. For now, this is
             the easiest way to preserve correct pane scrolling and searchbar collapse/expand
             behaviors. -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/action_bar_height_large"
            android:background="@color/actionbar_background_color"
            android:elevation="@dimen/tab_elevation" />

        <com.android.contacts.common.list.ViewPagerTabs
            android:id="@+id/lists_pager_header"
            android:layout_width="match_parent"
            android:layout_height="@dimen/tab_height"
            android:textAllCaps="true"
            android:orientation="horizontal"
            android:layout_gravity="top"
            android:elevation="@dimen/tab_elevation"
            style="@style/DialtactsActionBarTabTextStyle" />

        <android.support.v4.view.ViewPager
            android:id="@+id/lists_pager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

    </LinearLayout>

    <!-- Sets android:importantForAccessibility="no" to avoid being announced when navigating with
         talkback enabled. It will still be announced when user drag or drop contact onto it.
         This is required since drag and drop event is only sent to views are visible when drag
         starts. -->
    <com.android.dialer.list.RemoveView
        android:id="@+id/remove_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/tab_height"
        android:layout_marginTop="@dimen/action_bar_height_large"
        android:contentDescription="@string/remove_contact"
        android:importantForAccessibility="no" >

        <LinearLayout
            android:id="@+id/remove_view_content"
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:background="@color/actionbar_background_color"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:id="@+id/remove_view_icon"
                android:src="@drawable/ic_remove"
                android:tint="@color/remove_text_color" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/remove_view_text"
                android:textSize="@dimen/remove_text_size"
                android:textColor="@color/remove_text_color"
                android:text="@string/remove_contact" />

        </LinearLayout>

    </com.android.dialer.list.RemoveView >

</FrameLayout>
