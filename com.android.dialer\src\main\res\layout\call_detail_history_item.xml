<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/call_log_inner_margin"
    android:paddingBottom="@dimen/call_log_inner_margin"
    android:paddingStart="@dimen/call_detail_horizontal_margin"
    android:paddingEnd="@dimen/call_log_outer_margin"
    android:orientation="vertical" >
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal" >
        <view
            class="com.android.dialer.calllog.CallTypeIconsView"
            android:id="@+id/call_type_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical" />
        <TextView
            android:id="@+id/call_type_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/call_log_icon_margin"
            android:textColor="?attr/call_log_primary_text_color"
            android:textSize="@dimen/call_log_primary_text_size" />
    </LinearLayout>
    <TextView
        android:id="@+id/date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/call_log_secondary_text_color"
        android:textSize="@dimen/call_log_detail_text_size" />
    <TextView
        android:id="@+id/duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/call_log_secondary_text_color"
        android:textSize="@dimen/call_log_detail_text_size" />
</LinearLayout>
